package im.cu.api.shutdown.grace;

import lombok.Data;

/**
 * 优雅停机配置类
 * <p>
 * 提供优雅停机相关的配置选项，包括：
 * - 超时时间配置
 * - 日志控制配置
 * - 失败处理策略
 * <p>
 * 适用场景：
 * - Tomcat 9 + Spring MVC 4.2
 * - 需要自定义优雅停机参数
 * <p>
 * 配置方式：
 * - 通过Spring的@Value注解注入
 * - 通过properties文件配置
 * - 通过Java配置类设置
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Data
public class GracefulShutdownConfiguration {

    /**
     * 是否启用优雅停机
     * 默认启用
     */
    private boolean enabled = true;

    /**
     * HTTP请求完成等待超时时间（毫秒）
     * 等待现有HTTP请求处理完成的最大时间
     * 默认30秒
     */
    private long httpRequestTimeoutMs = 30000;

    /**
     * 是否启用详细日志
     * 启用后会输出详细的停机过程日志
     * 默认启用
     */
    private boolean verboseLogging = true;

    /**
     * 是否等待RPC调用完成
     * 在大多数情况下，RPC调用在HTTP请求中同步执行，无需单独等待
     * 但如果有异步RPC调用、定时任务、消息队列等场景，可以启用此选项
     * 默认关闭
     */
    private boolean waitForRpcCalls = false;

    /**
     * 停机时拒绝新请求的重试建议时间（秒）
     * 用于设置HTTP响应的Retry-After头
     * 默认30秒
     */
    private int retryAfterSeconds = 30;

    /**
     * 是否启用JVM关闭钩子
     * 作为额外的保护机制，在JVM关闭时触发优雅停机
     * 默认启用
     */
    private boolean enableShutdownHook = true;

    /**
     * 是否允许停机失败
     * true: 停机过程中出现异常时继续停机流程
     * false: 停机过程中出现异常时抛出异常
     * 默认true，避免阻塞停机流程
     */
    private boolean shutdownFailureAllowed = true;

    /**
     * 请求跟踪过滤器的详细日志
     * 控制RequestTrackingFilter是否输出详细日志
     * 默认false，避免日志过多
     */
    private boolean requestTrackingVerbose = false;

    /**
     * 停机状态检查间隔（毫秒）
     * 检查请求完成状态的间隔时间
     * 默认1秒
     */
    private long statusCheckIntervalMs = 1000;

    /**
     * 最大等待周期数
     * 防止无限等待的保护机制
     * 默认60个周期（配合1秒间隔，总共60秒）
     */
    private int maxWaitCycles = 60;

    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (httpRequestTimeoutMs <= 0) {
            throw new IllegalArgumentException("httpRequestTimeoutMs must be positive");
        }

        if (statusCheckIntervalMs <= 0) {
            throw new IllegalArgumentException("statusCheckIntervalMs must be positive");
        }
        if (maxWaitCycles <= 0) {
            throw new IllegalArgumentException("maxWaitCycles must be positive");
        }
    }

    /**
     * 获取总的停机超时时间
     */
    public long getTotalShutdownTimeoutMs() {
        return httpRequestTimeoutMs + 5000; // 额外5秒缓冲
    }

    /**
     * 创建默认配置
     */
    public static GracefulShutdownConfiguration createDefault() {
        GracefulShutdownConfiguration config = new GracefulShutdownConfiguration();
        config.validate();
        return config;
    }

    /**
     * 创建用于生产环境的配置
     */
    public static GracefulShutdownConfiguration createForProduction() {
        GracefulShutdownConfiguration config = new GracefulShutdownConfiguration();
        config.setHttpRequestTimeoutMs(60000); // 生产环境增加到60秒

        config.setVerboseLogging(false); // 生产环境关闭详细日志
        config.setRequestTrackingVerbose(false); // 生产环境关闭请求跟踪详细日志
        config.validate();
        return config;
    }

    /**
     * 创建用于开发环境的配置
     */
    public static GracefulShutdownConfiguration createForDevelopment() {
        GracefulShutdownConfiguration config = new GracefulShutdownConfiguration();
        config.setHttpRequestTimeoutMs(10000); // 开发环境缩短到10秒

        config.setVerboseLogging(true); // 开发环境启用详细日志
        config.setRequestTrackingVerbose(true); // 开发环境启用请求跟踪详细日志
        config.validate();
        return config;
    }

    @Override
    public String toString() {
        return String.format("GracefulShutdownConfiguration{" +
                "enabled=%s, " +
                "httpRequestTimeoutMs=%d, " +
                "verboseLogging=%s, " +
                "totalTimeoutMs=%d}",
                enabled, httpRequestTimeoutMs,
                verboseLogging, getTotalShutdownTimeoutMs());
    }

    /**
     * 是否需要等待RPC调用完成
     */
    public boolean isWaitForRpcCalls() {
        return waitForRpcCalls;
    }

    /**
     * 设置是否需要等待RPC调用完成
     */
    public void setWaitForRpcCalls(boolean waitForRpcCalls) {
        this.waitForRpcCalls = waitForRpcCalls;
    }
}
