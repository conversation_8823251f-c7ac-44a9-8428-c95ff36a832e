package im.cu.api.shutdown.grace;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 优雅停机健康检查控制器
 * <p>
 * 职责：
 * - 提供应用停机状态的健康检查端点
 * - 为负载均衡器和监控系统提供状态信息
 * - 支持Kubernetes的readiness和liveness探针
 * <p>
 * 适用场景：
 * - Kubernetes环境下的健康检查
 * - 负载均衡器的健康检查
 * - 监控系统的状态监控
 * <p>
 * 端点说明：
 * - /graceful-shutdown/readiness: Kubernetes readiness探针
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
@Slf4j
@RestController
@RequestMapping("/graceful-shutdown")
public class GracefulShutdownHealthController {

    @Autowired(required = false)
    private GracefulShutdownConfiguration config;

    /**
     * 基础健康检查端点
     * 返回应用是否正在停机
     */
    @RequestMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();

        boolean isShuttingDown = RequestTrackingFilter.isShuttingDown();

        response.put("status", isShuttingDown ? "SHUTTING_DOWN" : "UP");
        response.put("shuttingDown", isShuttingDown);
        response.put("timestamp", System.currentTimeMillis());

        // 如果正在停机，返回503状态码，让负载均衡器移除此实例
        HttpStatus status = isShuttingDown ? HttpStatus.SERVICE_UNAVAILABLE : HttpStatus.OK;

        return ResponseEntity.status(status).body(response);
    }


    /**
     * Kubernetes readiness探针端点
     * 用于Kubernetes的readiness检查
     */
    @RequestMapping("/readiness")
    public ResponseEntity<Map<String, Object>> readiness() {
        Map<String, Object> response = new HashMap<>();

        boolean isShuttingDown = RequestTrackingFilter.isShuttingDown();
        boolean isPreparing = isPreparingShutdown();
        boolean isReady = !isShuttingDown && !isPreparing;

        response.put("ready", isReady);
        response.put("status", isReady ? "READY" : "NOT_READY");
        response.put("timestamp", System.currentTimeMillis());

        if (!isReady) {
            if (isPreparing) {
                response.put("reason", "Application is preparing for shutdown");
            } else {
                response.put("reason", "Application is shutting down gracefully");
            }
            int activeRequests = RequestTrackingFilter.getActiveRequestCount();
            response.put("activeRequests", activeRequests);
        }

        HttpStatus status = isReady ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
        return ResponseEntity.status(status).body(response);
    }

    /**
     * 准备停机端点
     * 由 preStop 钩子调用，让应用进入准备停机状态
     */
    @RequestMapping("/prepare")
    public ResponseEntity<Map<String, Object>> prepareShutdown() {
        log.info(">>>>> 收到停机准备请求（preStop钩子调用）");

        setPreparingShutdown(true);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Application is now preparing for shutdown");
        response.put("status", "PREPARING");
        response.put("timestamp", System.currentTimeMillis());
        response.put("activeRequests", RequestTrackingFilter.getActiveRequestCount());

        log.info(">>>>> 应用已进入停机准备状态，readiness检查将返回NOT_READY");
        return ResponseEntity.ok(response);
    }

    /**
     * 停机准备状态
     */
    private static volatile boolean preparingShutdown = false;

    private static boolean isPreparingShutdown() {
        return preparingShutdown;
    }

    private static void setPreparingShutdown(boolean preparing) {
        preparingShutdown = preparing;
    }

    /**
     * 获取请求统计信息
     */
    @RequestMapping("/statistics")
    public ResponseEntity<Map<String, Object>> statistics() {
        Map<String, Object> response = new HashMap<>();

        response.put("activeRequests", RequestTrackingFilter.getActiveRequestCount());
        response.put("totalRequests", RequestTrackingFilter.getTotalRequestCount());
        response.put("shuttingDown", RequestTrackingFilter.isShuttingDown());
        response.put("statistics", RequestTrackingFilter.getStatistics());
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * 专门的 Ingress 健康检查端点
     * 可以独立控制是否从 Ingress 负载均衡器摘除
     */
    @RequestMapping("/health/ingress")
    public ResponseEntity<Map<String, Object>> ingressHealth() {
        Map<String, Object> response = new HashMap<>();

        boolean isIngressRemoved = isIngressRemoved();
        boolean isHealthy = !isIngressRemoved && !RequestTrackingFilter.isShuttingDown();

        response.put("healthy", isHealthy);
        response.put("status", isHealthy ? "UP" : "DOWN");
        response.put("timestamp", System.currentTimeMillis());

        if (!isHealthy) {
            if (isIngressRemoved) {
                response.put("reason", "Manually removed from Ingress load balancer");
            } else {
                response.put("reason", "Application is shutting down");
            }
        }

        HttpStatus status = isHealthy ? HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;
        return ResponseEntity.status(status).body(response);
    }

    /**
     * 从 Ingress 负载均衡器摘除端点
     * 专门用于主动摘除，不影响应用的正常运行
     */
    @RequestMapping("/ingress/remove")
    public ResponseEntity<Map<String, Object>> removeFromIngress() {
        log.info(">>>>> 收到从 Ingress 摘除请求");

        setIngressRemoved(true);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Pod removed from Ingress load balancer");
        response.put("status", "REMOVED");
        response.put("timestamp", System.currentTimeMillis());
        response.put("note", "Application continues running, only removed from load balancer");

        log.info(">>>>> Pod 已从 Ingress 负载均衡器摘除");
        return ResponseEntity.ok(response);
    }

    /**
     * 恢复到 Ingress 负载均衡器
     */
    @RequestMapping("/ingress/restore")
    public ResponseEntity<Map<String, Object>> restoreToIngress() {
        log.info(">>>>> 收到恢复到 Ingress 请求");

        setIngressRemoved(false);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Pod restored to Ingress load balancer");
        response.put("status", "RESTORED");
        response.put("timestamp", System.currentTimeMillis());

        log.info(">>>>> Pod 已恢复到 Ingress 负载均衡器");
        return ResponseEntity.ok(response);
    }

    /**
     * 手动触发优雅停机（仅用于测试）
     * 生产环境应该通过SIGTERM信号触发
     */
    @RequestMapping("/trigger-shutdown")
    public ResponseEntity<Map<String, Object>> triggerShutdown() {
        if (config != null && !config.isEnabled()) {
            Map<String, Object> response = new HashMap<>();
            response.put("error", "Graceful shutdown is disabled");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
        }

        log.warn(">>>>> 手动触发优雅停机（仅用于测试）");
        RequestTrackingFilter.setShuttingDown(true);

        Map<String, Object> response = new HashMap<>();
        response.put("message", "Graceful shutdown triggered manually");
        response.put("timestamp", System.currentTimeMillis());

        return ResponseEntity.ok(response);
    }

    /**
     * Ingress 摘除状态
     */
    private static volatile boolean ingressRemoved = false;

    private static boolean isIngressRemoved() {
        return ingressRemoved;
    }

    private static void setIngressRemoved(boolean removed) {
        ingressRemoved = removed;
    }
}
