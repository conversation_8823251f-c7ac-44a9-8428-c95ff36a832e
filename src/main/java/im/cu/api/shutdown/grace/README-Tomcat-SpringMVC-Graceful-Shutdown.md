# Tomcat + Spring MVC 优雅停机框架

## 概述

本框架专门为 **Tomcat 9 + Spring MVC 4.2 + Dubbo Consumer** 场景设计，提供完整的优雅停机解决方案，确保在 Kubernetes 环境下应用能够优雅地停止服务，避免请求丢失。

### 场景特点

- **Tomcat 9**：传统的 Servlet 容器，需要手动配置优雅停机
- **Kubernetes 部署**：需要配合 K8s 的优雅停机机制

### 核心设计理念

**停机顺序**：

1. 停止接收新的 HTTP 请求
2. 等待现有 HTTP 请求处理完成
3. Dubbo 由自身的 ShutdownHook 处理关闭
4. 关闭 Spring 容器和 Tomcat

**关键优势**：

- ✅ **专门适配**：针对传统 Spring MVC 架构设计
- ✅ **请求跟踪**：自实现 HTTP 请求生命周期跟踪
- ✅ **简化架构**：移除复杂的 Dubbo 管理，让 Dubbo 自管理
- ✅ **K8s 就绪**：完美配合 Kubernetes 部署
- ✅ **避免竞态**：避免 JVM ShutdownHook 执行顺序冲突

## 核心组件

### 1. RequestTrackingFilter

- 跟踪活跃的 HTTP 请求数量
- 在停机时拒绝新请求（返回 503 状态码）
- 提供请求完成等待机制
- 支持配置化的重试建议时间
- 优化的同步机制，提高并发性能

### 2. GracefulShutdownManager

- 监听 Spring 容器关闭事件
- 协调整个停机流程
- 控制各组件的关闭顺序
- 增强的异常处理和空指针保护

### 3. TomcatGracefulShutdownMonitor（可选）

- 提供监控和统计功能
- 不依赖 Tomcat 内部类，适用于 WAR 部署

### 4. GracefulShutdownWebConfig

- 自动配置所有组件
- 支持 properties 文件配置
- 专注 HTTP 请求优雅停机配置
- 增强的配置验证和错误处理

### 5. GracefulShutdownHealthController

- 提供健康检查端点
- 支持 Kubernetes readiness/liveness 探针
- HTTP 请求状态信息和统计数据
- 手动触发停机功能（测试用）

### 6. GracefulShutdownTestUtils

- 完整的测试工具集
- 并发请求模拟
- 配置验证工具
- 状态快照功能

## Kubernetes 优雅停机的关键问题

### ⚠️ **流量路由时间差问题**

在 Kubernetes 环境中，存在一个关键的时序问题：

```
时间轴：Kubernetes 优雅停机流程
T0: Pod 标记为 Terminating
T1: preStop 钩子执行 (⚠️ 流量仍在路由到该 Pod!)
T2: SIGTERM 发送 + 应用开始停机
T3: EndpointSlice 更新 (可能延迟几秒)
T4: kube-proxy 更新规则
T5: Ingress 控制器更新配置
T6: 流量真正停止路由到该 Pod

危险窗口：T2-T6 期间，应用已停机但仍可能收到请求！
```

### 🛡️ **必须使用 preStop 钩子的原因**

1. **主动通知应用准备停机**：让 readiness 检查失败
2. **给 Kubernetes 网络更新时间**：等待 EndpointSlice 和 Ingress 更新
3. **避免请求丢失**：确保流量切走后再开始真正停机

### � **主动从 Ingress 摘除的方案**

#### 方案 1：通过 Readiness 检查摘除（推荐）

**原理**：Ingress Controller 监控 Service 的 Endpoints，当 Pod readiness 失败时，自动从负载均衡器摘除。

**优势**：

- ✅ 通用性强，适用于所有 Ingress Controller
- ✅ 实现简单，无需额外配置
- ✅ 自动化程度高

**实现**：

```bash
# preStop 钩子中调用
curl -X POST http://localhost:8080/graceful-shutdown/prepare
# 此时 readiness 检查返回 NOT_READY
# Ingress Controller 在 5-10 秒内自动摘除该 Pod
```

**时序图**：

```
T0: preStop 执行 → /prepare 调用
T1: readiness 返回 NOT_READY (立即)
T2: kubelet 更新 Pod 状态 (1-2秒)
T3: Service Endpoints 更新 (2-3秒)
T4: Ingress Controller 检测到变化 (5-10秒)
T5: 负载均衡器配置更新 (立即)
T6: 流量停止路由到该 Pod ✅
```

#### 方案 2：直接调用 Ingress Controller API

**适用场景**：需要更快的摘除速度，或者需要精确控制。

**Nginx Ingress Controller**：

```bash
# preStop 钩子中调用 Nginx Ingress API
NGINX_POD=$(kubectl get pods -n ingress-nginx -l app.kubernetes.io/name=ingress-nginx -o jsonpath='{.items[0].metadata.name}')
kubectl exec -n ingress-nginx $NGINX_POD -- nginx -s reload

# 或者通过 Nginx Plus API (如果使用商业版)
curl -X POST "http://nginx-controller/api/6/http/upstreams/backend/servers" \
  -d '{"server":"'$POD_IP':8080","down":true}'
```

**Traefik Ingress Controller**：

```bash
# 通过 Traefik API 摘除后端
curl -X PUT "http://traefik-api:8080/api/providers/kubernetes/endpoints/default-graceful-shutdown-demo-service-80" \
  -H "Content-Type: application/json" \
  -d '{"servers":[]}'
```

**时序图**：

```
T0: preStop 执行 → 直接调用 Ingress API
T1: Ingress Controller 立即更新配置 (1-2秒)
T2: 负载均衡器配置生效 (立即)
T3: 流量停止路由到该 Pod ✅ (总计2-3秒)
```

#### 方案 3：通过 Service 标签操作

**原理**：临时修改 Pod 标签，使其不匹配 Service selector。

```bash
# preStop 钩子中执行
# 1. 获取当前 Pod 信息
POD_NAME=$(hostname)
NAMESPACE=${POD_NAMESPACE:-default}

# 2. 添加一个临时标签，使其不匹配 Service selector
kubectl label pod $POD_NAME graceful-shutdown=true --overwrite

# 3. 等待 Service Endpoints 更新
sleep 5
```

**对应的 Service 配置**：

```yaml
apiVersion: v1
kind: Service
metadata:
  name: graceful-shutdown-demo-service
spec:
  selector:
    app: graceful-shutdown-demo
    graceful-shutdown: "!true" # 排除正在停机的 Pod
```

#### 方案 4：通过健康检查端点控制

**实现一个专门的摘除端点**：

```java
@PostMapping("/ingress/remove")
public ResponseEntity<Map<String, Object>> removeFromIngress() {
    // 设置一个特殊的状态，让健康检查失败
    setIngressRemoved(true);

    Map<String, Object> response = new HashMap<>();
    response.put("message", "Pod removed from Ingress load balancer");
    response.put("timestamp", System.currentTimeMillis());

    return ResponseEntity.ok(response);
}

@GetMapping("/health/ingress")
public ResponseEntity<String> ingressHealth() {
    if (isIngressRemoved()) {
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body("Pod removed from load balancer");
    }
    return ResponseEntity.ok("OK");
}
```

**Ingress 配置使用专门的健康检查**：

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/upstream-health-check: "true"
    nginx.ingress.kubernetes.io/upstream-health-check-path: "/health/ingress"
```

### �📋 **完整的 preStop 配置**

```yaml
lifecycle:
  preStop:
    exec:
      command:
        - /bin/sh
        - -c
        - |
          # 1. 通知应用准备停机（readiness 检查失败）
          curl -X POST http://localhost:8080/graceful-shutdown/prepare

          # 2. 等待 Kubernetes 网络拓扑更新
          sleep 15

          # 3. 现在应用可以安全地开始停机了
```

### 🏆 **推荐的综合方案**

**结合多种方法，确保最快速度摘除**：

```yaml
lifecycle:
  preStop:
    exec:
      command:
        - /bin/sh
        - -c
        - |
          echo "=== 开始主动摘除流程 ==="

          # 方案1: 通知应用准备停机（readiness 失败）
          echo "1. 设置 readiness 为 NOT_READY..."
          curl -f -X POST http://localhost:8080/graceful-shutdown/prepare || echo "准备停机失败"

          # 等待网络拓扑更新
          echo "2. 等待网络拓扑更新..."
          sleep 15

          echo "=== 主动摘除流程完成 ==="
```

### ⚡ **各方案对比**

| 方案           | 摘除速度 | 实现复杂度 | 通用性 | 推荐度     |
| -------------- | -------- | ---------- | ------ | ---------- |
| Readiness 检查 | 5-10 秒  | 简单       | 高     | ⭐⭐⭐⭐⭐ |
| Ingress API    | 2-3 秒   | 复杂       | 低     | ⭐⭐⭐     |
| Service 标签   | 3-5 秒   | 中等       | 中     | ⭐⭐⭐⭐   |
| 专门健康检查   | 5-10 秒  | 中等       | 中     | ⭐⭐⭐     |

### 🎯 **最佳实践总结**

1. **生产环境推荐**：使用 Readiness 检查方案

   - 简单可靠，适用于所有 Ingress Controller
   - 无需额外权限或复杂配置

2. **高性能场景**：结合 Readiness + Service 标签

   - 更快的摘除速度
   - 双重保险

3. **特殊需求**：直接调用 Ingress API
   - 最快的摘除速度
   - 需要深度定制

## 设计决策说明

### 为什么专注 HTTP 请求优雅停机？

**设计理念**：

- **HTTP 请求是用户直接感知的**：用户通过 HTTP 接口与应用交互
- **Dubbo 自管理关闭**：Dubbo 的 ShutdownHook 会正确处理 RPC 连接关闭

## Kubernetes 部署配置

### 🚀 **完整的 K8s 部署示例**

1. **Deployment 配置**

   - preStop 钩子：通知应用准备停机 + 等待网络更新
   - 健康检查：liveness 和 readiness 探针
   - 优雅停机时间：`terminationGracePeriodSeconds: 60`

2. **Service 配置**

   - ClusterIP 服务暴露应用

3. **Ingress 配置**

   - Nginx Ingress 超时配置
   - 流量路由规则

4. **PodDisruptionBudget**
   - 确保滚动更新时的可用性

### ⚡ **关键配置说明**

```yaml
# 1. preStop 钩子 - 最重要的配置
lifecycle:
  preStop:
    exec:
      command: ["/bin/sh", "-c"]
      args:
        - |
          curl -X POST http://localhost:8080/graceful-shutdown/prepare
          sleep 15

# 2. readiness 探针 - 配合 preStop 使用
readinessProbe:
  httpGet:
    path: /graceful-shutdown/readiness
    port: 8080
  periodSeconds: 5
  failureThreshold: 2

# 3. 优雅停机时间 - 必须足够长
terminationGracePeriodSeconds: 60
```

### 🔄 **部署流程**

```bash
# 1. 部署应用
kubectl apply -f src/main/resources/k8s/graceful-shutdown-deployment.yaml

# 2. 验证部署
kubectl get pods -l app=graceful-shutdown-demo
kubectl get svc graceful-shutdown-demo-service

# 3. 测试优雅停机
kubectl delete pod <pod-name>
# 观察日志，确认 preStop 钩子执行
kubectl logs <pod-name> -f
```
## 停机流程详解

### 1. 触发条件

- 收到 SIGTERM 信号（Kubernetes 发送）
- - JVM 关闭钩子（备用机制）
- Spring 容器关闭事件

### 2. 执行步骤

```
SIGTERM信号 → Spring ContextClosedEvent
    ↓
GracefulShutdownManager.onApplicationEvent()
    ↓
步骤1: RequestTrackingFilter.setShuttingDown(true)
    ├── 停止接收新HTTP请求
    └── 返回503状态码给新请求
    ↓
步骤2: RequestTrackingFilter.waitForRequestsToComplete()
    ├── 等待活跃请求数归零
    ├── 超时时间: http-request-timeout-ms
    └── 每1秒检查一次状态
    ↓
步骤3: Dubbo自动关闭（并发执行）
    ├── Dubbo ShutdownHook 自动触发
    ├── 停止接收新的RPC请求
    ├── 等待正在进行的RPC调用完成
    └── 关闭网络连接和清理资源
    ↓
步骤4: Spring容器继续关闭流程
    ↓
Tomcat关闭
```

### 3. 日志输出

启用详细日志后，停机过程会输出：

```
>>>>> 收到Spring容器关闭事件，开始执行优雅停机流程...
>>>>> 步骤1: 设置停机状态，停止接收新的HTTP请求...
>>>>> 已设置停机状态，新请求将被拒绝
>>>>> 当前请求状态 - 活跃请求数: 3, 总处理请求数: 157
>>>>> 步骤2: 等待现有HTTP请求处理完成...
>>>>> 请求结束，剩余活跃请求数: 2
>>>>> 请求结束，剩余活跃请求数: 1
>>>>> 请求结束，剩余活跃请求数: 0
>>>>> 所有HTTP请求已处理完成，活跃请求数: 0
>>>>> HTTP请求处理完成
>>>>> 步骤3: 关闭Dubbo Consumer连接...
>>>>> 开始优雅关闭Dubbo Consumer...
>>>>> 等待10000ms让正在进行的RPC调用完成...
>>>>> 关闭Dubbo Consumer连接...
>>>>> 通过ApplicationConfig.destroyAll()关闭Dubbo成功
>>>>> Dubbo Consumer 优雅关闭完成
>>>>> Dubbo Consumer关闭完成
>>>>> 优雅停机流程完成，总耗时: 5234ms
```

## Kubernetes 配置

### 1. Deployment 配置

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: your-app
spec:
  template:
    spec:
      containers:
        - name: your-app
          image: your-app:latest
          ports:
            - containerPort: 8080
          # 优雅停机配置
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 5"]
          # 就绪探针
          readinessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 10
            periodSeconds: 5
            timeoutSeconds: 3
            failureThreshold: 3
          # 存活探针
          livenessProbe:
            httpGet:
              path: /health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          # 资源限制
          resources:
            requests:
              memory: "512Mi"
              cpu: "500m"
            limits:
              memory: "1Gi"
              cpu: "1000m"
      # 优雅停机超时时间
      terminationGracePeriodSeconds: 60
```

### 2. Service 配置

```yaml
apiVersion: v1
kind: Service
metadata:
  name: your-app-service
spec:
  selector:
    app: your-app
  ports:
    - name: http
      port: 8080
      targetPort: 8080
      protocol: TCP
  type: ClusterIP
```

### 3. 健康检查端点

创建一个简单的健康检查 Controller：

```java
@RestController
public class HealthController {

    @Autowired(required = false)
    private GracefulShutdownManager gracefulShutdownManager;

    @RequestMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> status = new HashMap<>();
        status.put("status", "UP");
        status.put("timestamp", System.currentTimeMillis());

        if (gracefulShutdownManager != null) {
            status.put("shutdownInProgress", gracefulShutdownManager.isShutdownInProgress());
            status.put("requestStats", RequestTrackingFilter.getStatistics());
        }

        return ResponseEntity.ok(status);
    }

    @RequestMapping("/ready")
    public ResponseEntity<String> ready() {
        // 如果正在停机，返回503
        if (RequestTrackingFilter.isShuttingDown()) {
            return ResponseEntity.status(503).body("Shutting down");
        }
        return ResponseEntity.ok("Ready");
    }
}
```

## 配置参数详解

| 参数                                               | 默认值  | 说明                              |
| -------------------------------------------------- | ------- | --------------------------------- |
| `finka.graceful-shutdown.enabled`                  | `true`  | 是否启用优雅停机                  |
| `finka.graceful-shutdown.http-request-timeout-ms`  | `30000` | HTTP 请求完成等待超时时间（毫秒） |
| `finka.graceful-shutdown.verbose-logging`          | `true`  | 是否启用详细日志                  |
| `finka.graceful-shutdown.request-tracking-verbose` | `false` | 是否启用请求跟踪详细日志          |
| `finka.graceful-shutdown.shutdown-failure-allowed` | `true`  | 是否允许停机失败                  |

## 最佳实践

### 1. 超时时间配置

```properties
# 根据业务请求处理时间调整
finka.graceful-shutdown.http-request-timeout-ms=30000

# Kubernetes配置应该大于HTTP请求超时时间
# terminationGracePeriodSeconds: 45  # 30s + 15s缓冲
```

### 2. 日志配置

```properties
# 生产环境建议关闭详细日志
finka.graceful-shutdown.verbose-logging=false
finka.graceful-shutdown.request-tracking-verbose=false

# 开发环境可以启用详细日志
finka.graceful-shutdown.verbose-logging=true
finka.graceful-shutdown.request-tracking-verbose=true
```

### 3. 监控和告警

- 监控 `/health` 端点的响应
- 监控应用日志中的停机流程
- 设置告警监控停机超时情况

### 4. 测试验证

```bash
# 1. 启动应用
java -jar your-app.jar

# 2. 发送测试请求
curl http://localhost:8080/your-api

# 3. 在处理请求时停止应用
kill -TERM <pid>

# 4. 观察日志输出，验证优雅停机流程
```

## 高级配置

### 1. 自定义配置类

```java
@Configuration
public class CustomGracefulShutdownConfig {

    @Bean
    public GracefulShutdownConfiguration gracefulShutdownConfiguration() {
        GracefulShutdownConfiguration config = new GracefulShutdownConfiguration();

        // 生产环境配置
        if (isProductionEnvironment()) {
            config.setHttpRequestTimeoutMs(60000);  // 60秒
            config.setVerboseLogging(false);
        } else {
            // 开发环境配置
            config.setHttpRequestTimeoutMs(10000);   // 10秒
            config.setVerboseLogging(true);
        }

        return config;
    }

    private boolean isProductionEnvironment() {
        return "prod".equals(System.getProperty("spring.profiles.active"));
    }
}
```

### 2. 监控端点配置（可选）

创建监控 Controller 来暴露优雅停机状态：

```java
@RestController
@RequestMapping("/actuator")
public class GracefulShutdownController {

    @Autowired(required = false)
    private TomcatGracefulShutdownMonitor monitor;

    @RequestMapping("/graceful-shutdown")
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();

        if (monitor != null) {
            status.put("monitorStatus", monitor.getStatusInfo());
            status.put("requestStats", monitor.getRequestStatistics());
            status.put("uptime", monitor.getUptime());
            status.put("healthy", monitor.isHealthy());
        }

        status.put("shutdownInProgress", RequestTrackingFilter.isShuttingDown());
        status.put("activeRequests", RequestTrackingFilter.getActiveRequestCount());

        return status;
    }
}
```

**为什么不使用 Tomcat Valve？**

在传统的 WAR 部署方式中，应用代码不能直接依赖 Tomcat 的内部类（如`org.apache.catalina.valves.ValveBase`），因为：

1. **类路径隔离**：WAR 应用运行在 Tomcat 的 Web 应用类加载器中，无法访问 Tomcat 的内部类
2. **部署限制**：不同版本的 Tomcat 内部 API 可能不兼容
3. **可移植性**：应用应该能够在不同的 Servlet 容器中运行

因此我们使用纯 Servlet API 的 Filter 方式，确保最大的兼容性。

### 3. 自定义关闭逻辑

由于 Dubbo 由自身的 ShutdownHook 管理，如果需要自定义关闭逻辑，建议：

```java
@Component
public class CustomShutdownHandler {

    @PreDestroy
    public void beforeShutdown() {
        log.info("执行自定义关闭前处理");
        // 自定义逻辑：比如发送关闭通知、清理缓存等
    }
}
```

或者在 Service 层处理：

```java
@Service
public class UserService {

    @PreDestroy
    public void cleanup() {
        log.info("清理 UserService 相关资源");
        // 清理本地缓存、关闭连接池等
    }
}
```

## 故障排查

### 1. 请求仍然丢失

**可能原因：**

- HTTP 请求等待时间不够
- 负载均衡器没有及时更新
- 就绪探针配置不当

**解决方案：**

```properties
# 增加HTTP请求等待时间
finka.graceful-shutdown.http-request-timeout-ms=60000

# 确保Kubernetes配置正确
terminationGracePeriodSeconds: 90

# 检查就绪探针配置
readinessProbe:
  failureThreshold: 1  # 快速失败
  periodSeconds: 2     # 频繁检查
```

### 2. Dubbo 关闭失败

**可能原因：**

- Dubbo 版本不兼容
- 没有正确的 Dubbo 依赖
- RPC 调用超时

**解决方案：**

```java
// 检查Dubbo版本兼容性
@Component
public class DubboVersionChecker {

    @PostConstruct
    public void checkDubboVersion() {
        try {
            Class.forName("com.alibaba.dubbo.config.ApplicationConfig");
            log.info("检测到Dubbo 2.x版本");
        } catch (ClassNotFoundException e1) {
            try {
                Class.forName("org.apache.dubbo.config.ApplicationConfig");
                log.info("检测到Dubbo 3.x版本");
            } catch (ClassNotFoundException e2) {
                log.warn("未检测到Dubbo依赖");
            }
        }
    }
}
```

### 3. 停机超时

**可能原因：**

- 有长时间运行的请求
- 数据库连接未正确关闭
- 线程池未正确关闭

**解决方案：**

```java
// 添加请求超时控制
@Component
public class RequestTimeoutController {

    @EventListener
    public void onShutdown(ContextClosedEvent event) {
        // 中断长时间运行的任务
        interruptLongRunningTasks();

        // 关闭线程池
        shutdownThreadPools();

        // 关闭数据库连接
        closeDatabaseConnections();
    }
}
```

### 4. 日志调试

启用详细日志进行调试：

```properties
# 启用所有相关日志
logging.level.com.finka.common.web.tomcat.grace=DEBUG
logging.level.org.springframework.context=DEBUG
logging.level.org.apache.catalina=INFO

# 启用详细的优雅停机日志
finka.graceful-shutdown.verbose-logging=true
finka.graceful-shutdown.request-tracking-verbose=true
```

### 5. 监控指标

创建监控端点：

```java
@RestController
@RequestMapping("/actuator")
public class GracefulShutdownMetricsController {

    @Autowired(required = false)
    private GracefulShutdownManager shutdownManager;

    @RequestMapping("/graceful-shutdown")
    public Map<String, Object> getMetrics() {
        Map<String, Object> metrics = new HashMap<>();

        metrics.put("shutdownInProgress", RequestTrackingFilter.isShuttingDown());
        metrics.put("activeRequests", RequestTrackingFilter.getActiveRequestCount());
        metrics.put("totalRequests", RequestTrackingFilter.getTotalRequestCount());

        if (shutdownManager != null) {
            metrics.put("managerStatus", shutdownManager.getStatusInfo());
        }

        return metrics;
    }
}
```

## 版本兼容性

- **Tomcat**: 9.0+
- **Spring MVC**: 4.2+
- **Dubbo**: 2.6+ 或 3.0+
- **Java**: 8+
- **Servlet API**: 3.1+

## 与 Spring Boot 版本的区别

| 特性           | Spring Boot 版本            | 传统 Spring MVC 版本      |
| -------------- | --------------------------- | ------------------------- |
| 自动配置       | ✅ 内置支持                 | ❌ 需要手动配置           |
| SmartLifecycle | ✅ 完整支持                 | ⚠️ 部分支持               |
| 请求跟踪       | ✅ 内置实现                 | ❌ 需要自实现             |
| 优雅停机       | ✅ server.shutdown=graceful | ❌ 需要自实现             |
| 配置方式       | ✅ application.yml          | ⚠️ properties + Java 配置 |

## 总结

**核心优势：**

1. **专门适配**：针对传统 Spring MVC + Tomcat 架构设计
2. **完整解决方案**：从 HTTP 请求到 Dubbo Consumer 的完整优雅停机
3. **Kubernetes 就绪**：完美配合 K8s 的部署和停机机制
4. **易于集成**：最小化配置，最大化兼容性
5. **生产验证**：经过充分测试，适用于生产环境

**适用场景：**

- ✅ 传统 Spring MVC 应用迁移到 Kubernetes
- ✅ 需要优雅停机但不想升级到 Spring Boot
- ✅ Dubbo Consumer 应用的优雅停机
- ✅ 对停机流程有精确控制需求的场景

这套框架为传统 Spring MVC 应用提供了与 Spring Boot 同等级别的优雅停机能力，确保在现代化部署环境中的稳定运行。

## 底层原理深度解析

### 1. Tomcat 请求处理原理

#### 1.1 Tomcat 架构概述

```
客户端请求 → Connector → Engine → Host → Context → Servlet
    ↓
HTTP请求解析 → 线程池分配 → Filter链 → Servlet处理 → 响应返回
```

**核心组件说明：**

- **Connector**：负责接收 HTTP 请求，解析协议
- **Engine**：Servlet 引擎，管理多个虚拟主机
- **Host**：虚拟主机，对应一个域名
- **Context**：Web 应用上下文，对应一个 WAR 包
- **Servlet**：具体的请求处理器

#### 1.2 请求生命周期详解

```java
// Tomcat请求处理的核心流程（简化版）
public class TomcatRequestProcessor {

    // 1. Connector接收请求
    public void acceptRequest(Socket socket) {
        // 从线程池获取工作线程
        ThreadPoolExecutor executor = getExecutor();
        executor.execute(() -> processRequest(socket));
    }

    // 2. 解析HTTP请求
    private void processRequest(Socket socket) {
        try {
            // 解析HTTP协议
            HttpServletRequest request = parseHttpRequest(socket);
            HttpServletResponse response = createResponse(socket);

            // 3. 分发到对应的Context
            Context context = findContext(request.getRequestURI());
            context.invoke(request, response);

        } catch (Exception e) {
            handleError(e);
        } finally {
            // 4. 关闭连接（或保持连接）
            closeConnection(socket);
        }
    }

    // 3. Context处理请求
    public void invoke(HttpServletRequest request, HttpServletResponse response) {
        // 执行Filter链
        FilterChain filterChain = createFilterChain();
        filterChain.doFilter(request, response);
    }
}
```

**关键理解点：**

1. **线程模型**：每个 HTTP 请求由线程池中的一个线程处理
2. **连接管理**：Connector 管理客户端连接的建立和关闭
3. **请求分发**：通过 URL 匹配找到对应的 Servlet 处理
4. **Filter 链**：请求在到达 Servlet 前会经过 Filter 链处理

#### 1.3 Tomcat 优雅停机原理

```java
// Tomcat优雅停机的内部实现（简化版）
public class TomcatGracefulShutdown {

    private final AtomicInteger activeRequests = new AtomicInteger(0);
    private volatile boolean shuttingDown = false;

    // 停机流程
    public void shutdown(long timeoutMs) {
        log.info("开始Tomcat优雅停机...");

        // 第一步：停止接收新连接
        stopAcceptingNewConnections();

        // 第二步：等待现有请求完成
        waitForActiveRequestsToComplete(timeoutMs);

        // 第三步：强制关闭剩余连接
        forceCloseRemainingConnections();

        // 第四步：关闭线程池
        shutdownThreadPool();
    }

    // 停止接收新连接
    private void stopAcceptingNewConnections() {
        shuttingDown = true;

        // 暂停Connector，不再接收新的Socket连接
        for (Connector connector : connectors) {
            connector.pause();
            log.info("Connector {} 已暂停接收新连接", connector.getPort());
        }
    }

    // 等待现有请求完成
    private void waitForActiveRequestsToComplete(long timeoutMs) {
        long deadline = System.currentTimeMillis() + timeoutMs;

        while (activeRequests.get() > 0 && System.currentTimeMillis() < deadline) {
            log.debug("等待 {} 个活跃请求完成...", activeRequests.get());

            try {
                Thread.sleep(1000); // 每秒检查一次
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        if (activeRequests.get() > 0) {
            log.warn("优雅停机超时，仍有 {} 个请求未完成", activeRequests.get());
        }
    }

    // 请求开始时调用
    public void requestStarted() {
        if (shuttingDown) {
            throw new IllegalStateException("Server is shutting down");
        }
        activeRequests.incrementAndGet();
    }

    // 请求结束时调用
    public void requestEnded() {
        activeRequests.decrementAndGet();
    }
}
```

**为什么需要我们自己实现？**

传统的 Tomcat 部署（非 Spring Boot）默认没有启用优雅停机，收到 SIGTERM 信号后会立即关闭，导致：

1. **连接中断**：正在处理的请求被强制中断
2. **数据丢失**：响应数据可能没有完全发送给客户端
3. **用户体验差**：客户端收到连接重置错误

### 2. Spring MVC 请求处理原理

#### 2.1 Spring MVC 架构概述

```
HTTP请求 → DispatcherServlet → HandlerMapping → HandlerAdapter → Controller → ViewResolver → 响应
```

**核心组件说明：**

- **DispatcherServlet**：前端控制器，所有请求的入口
- **HandlerMapping**：处理器映射器，根据 URL 找到对应的 Controller
- **HandlerAdapter**：处理器适配器，调用具体的 Controller 方法
- **Controller**：业务控制器，处理具体的业务逻辑
- **ViewResolver**：视图解析器，处理响应视图

#### 2.2 DispatcherServlet 处理流程

```java
// DispatcherServlet的核心处理逻辑（简化版）
public class DispatcherServlet extends HttpServlet {

    @Override
    protected void doDispatch(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 1. 检查是否为multipart请求
            processedRequest = checkMultipart(request);

            // 2. 根据请求URL找到对应的Handler
            HandlerExecutionChain mappedHandler = getHandler(processedRequest);
            if (mappedHandler == null) {
                noHandlerFound(processedRequest, response);
                return;
            }

            // 3. 获取Handler适配器
            HandlerAdapter ha = getHandlerAdapter(mappedHandler.getHandler());

            // 4. 执行拦截器的preHandle方法
            if (!mappedHandler.applyPreHandle(processedRequest, response)) {
                return;
            }

            // 5. 实际调用Controller方法
            ModelAndView mv = ha.handle(processedRequest, response, mappedHandler.getHandler());

            // 6. 执行拦截器的postHandle方法
            mappedHandler.applyPostHandle(processedRequest, response, mv);

            // 7. 处理分发结果（渲染视图）
            processDispatchResult(processedRequest, response, mappedHandler, mv, dispatchException);

        } catch (Exception ex) {
            // 8. 异常处理
            processHandlerException(request, response, handler, ex);
        } finally {
            // 9. 执行拦截器的afterCompletion方法
            if (mappedHandler != null) {
                mappedHandler.triggerAfterCompletion(request, response, null);
            }
        }
    }
}
```

**关键理解点：**

1. **单例模式**：DispatcherServlet 是单例的，所有请求共享同一个实例
2. **线程安全**：每个请求在独立的线程中处理，但共享同一个 Servlet 实例
3. **拦截器链**：请求会经过多个拦截器的处理
4. **异常处理**：统一的异常处理机制

#### 2.3 Spring 容器生命周期

```java
// Spring容器的生命周期管理（简化版）
public class ApplicationContextLifecycle {

    // 容器启动
    public void start() {
        // 1. 加载Bean定义
        loadBeanDefinitions();

        // 2. 实例化Bean
        instantiateBeans();

        // 3. 依赖注入
        injectDependencies();

        // 4. 初始化Bean
        initializeBeans();

        // 5. 发布容器启动事件
        publishEvent(new ContextRefreshedEvent(this));
    }

    // 容器关闭
    public void close() {
        // 1. 发布容器关闭事件
        publishEvent(new ContextClosedEvent(this));

        // 2. 调用Bean的destroy方法
        destroyBeans();

        // 3. 关闭BeanFactory
        closeBeanFactory();
    }
}
```

**为什么监听 ContextClosedEvent？**

1. **时机准确**：在 Bean 销毁前执行，可以访问所有 Spring 管理的对象
2. **统一入口**：无论是正常关闭还是异常关闭，都会触发此事件
3. **框架集成**：与 Spring 的生命周期完美集成

### 3. Servlet Filter 工作原理

#### 3.1 Filter 链执行机制

```java
// Filter链的执行原理（简化版）
public class FilterChainImpl implements FilterChain {

    private Filter[] filters;
    private int pos = 0;
    private Servlet servlet;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response)
            throws IOException, ServletException {

        // 如果还有Filter没有执行
        if (pos < filters.length) {
            Filter filter = filters[pos++];

            // 执行当前Filter
            filter.doFilter(request, response, this);
        } else {
            // 所有Filter都执行完了，调用Servlet
            servlet.service(request, response);
        }
    }
}

// 我们的RequestTrackingFilter在链中的位置
public class RequestTrackingFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        // 前置处理：请求计数+1
        int activeCount = activeRequests.incrementAndGet();
        log.debug("请求开始，活跃请求数: {}", activeCount);

        try {
            // 继续执行Filter链（包括后续的Filter和最终的Servlet）
            chain.doFilter(request, response);

        } finally {
            // 后置处理：请求计数-1（无论成功还是异常都会执行）
            int remainingCount = activeRequests.decrementAndGet();
            log.debug("请求结束，剩余活跃请求数: {}", remainingCount);
        }
    }
}
```

**关键理解点：**

1. **责任链模式**：每个 Filter 处理完后调用 chain.doFilter()传递给下一个
2. **前后处理**：Filter 可以在请求前后都进行处理
3. **异常安全**：finally 块确保计数器在任何情况下都能正确更新
4. **执行顺序**：Filter 的执行顺序由配置的 order 决定

#### 3.2 为什么用 Filter 而不是 Interceptor？

```java
// Spring MVC Interceptor的执行时机
public class HandlerInterceptor {

    // 在Controller方法执行前调用
    boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler);

    // 在Controller方法执行后，视图渲染前调用
    void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView);

    // 在整个请求完成后调用
    void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex);
}
```

**Filter vs Interceptor 对比：**

| 特性     | Filter                         | Interceptor              |
| -------- | ------------------------------ | ------------------------ |
| 执行时机 | Servlet 容器级别               | Spring MVC 级别          |
| 覆盖范围 | 所有请求（包括静态资源）       | 只有 Controller 请求     |
| 异常处理 | 可以捕获所有异常               | 只能处理 Controller 异常 |
| 配置方式 | web.xml 或@WebFilter           | Spring 配置              |
| 执行顺序 | Filter → Servlet → Interceptor | 固定顺序                 |

**我们选择 Filter 的原因：**

1. **覆盖面广**：能够跟踪所有 HTTP 请求，包括静态资源
2. **异常安全**：能够捕获整个请求处理过程中的异常
3. **独立性强**：不依赖 Spring MVC 的具体实现
4. **时机准确**：在请求的最外层进行计数，确保准确性

### 4. 线程模型和并发控制

#### 4.1 Tomcat 线程池模型

```java
// Tomcat的线程池配置（简化版）
public class TomcatThreadPool {

    // 默认线程池配置
    private int corePoolSize = 10;        // 核心线程数
    private int maximumPoolSize = 200;    // 最大线程数
    private int keepAliveTime = 60;       // 线程空闲时间
    private BlockingQueue<Runnable> workQueue = new LinkedBlockingQueue<>(100);

    public void processRequest(Socket socket) {
        // 提交任务到线程池
        executor.execute(() -> {
            try {
                // 请求计数+1
                activeRequests.incrementAndGet();

                // 处理HTTP请求
                handleHttpRequest(socket);

            } finally {
                // 请求计数-1
                activeRequests.decrementAndGet();
            }
        });
    }
}
```

**并发控制的关键点：**

1. **原子操作**：使用 AtomicInteger 确保计数器的线程安全
2. **内存可见性**：使用 volatile 确保停机状态的及时可见
3. **等待机制**：使用 wait/notify 实现高效的等待

#### 4.2 我们的并发控制实现

```java
public class ConcurrencyControlExample {

    // 线程安全的计数器
    private static final AtomicInteger activeRequests = new AtomicInteger(0);

    // 停机状态标志（volatile确保可见性）
    private static volatile boolean shuttingDown = false;

    // 等待所有请求完成
    public static boolean waitForRequestsToComplete(long timeoutMs) {
        long deadline = System.currentTimeMillis() + timeoutMs;

        while (activeRequests.get() > 0 && System.currentTimeMillis() < deadline) {
            try {
                synchronized (RequestTrackingFilter.class) {
                    // 使用wait/notify机制，避免忙等待
                    long waitTime = Math.min(1000, deadline - System.currentTimeMillis());
                    if (waitTime > 0) {
                        RequestTrackingFilter.class.wait(waitTime);
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }

        return activeRequests.get() == 0;
    }

    // 请求结束时通知等待的线程
    public void requestEnded() {
        int remaining = activeRequests.decrementAndGet();

        if (remaining == 0 && shuttingDown) {
            synchronized (RequestTrackingFilter.class) {
                // 通知所有等待的线程
                RequestTrackingFilter.class.notifyAll();
            }
        }
    }
}
```

### 5. JVM 关闭钩子机制

#### 5.1 JVM 关闭流程

```java
// JVM关闭流程的内部机制（简化版）
public class JVMShutdownProcess {

    public void shutdown() {
        // 1. 触发关闭钩子
        runShutdownHooks();

        // 2. 运行finalizer
        runFinalizers();

        // 3. 停止所有非守护线程
        stopNonDaemonThreads();

        // 4. 退出JVM
        halt();
    }

    private void runShutdownHooks() {
        // 并行执行所有注册的关闭钩子
        for (Thread hook : shutdownHooks) {
            hook.start();
        }

        // 等待所有钩子执行完成
        for (Thread hook : shutdownHooks) {
            try {
                hook.join();
            } catch (InterruptedException e) {
                // 忽略中断
            }
        }
    }
}
```

**关闭钩子的特点：**

1. **并行执行**：多个钩子会并行运行
2. **无序执行**：钩子的执行顺序不确定
3. **有限时间**：系统可能会强制终止钩子的执行
4. **异常隔离**：一个钩子的异常不会影响其他钩子

#### 5.2 我们的关闭钩子实现

```java
public class GracefulShutdownHook {

    public GracefulShutdownHook() {
        // 注册JVM关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("JVM关闭钩子被触发");

            try {
                // 如果Spring容器还没有开始关闭，我们主动触发
                if (!RequestTrackingFilter.isShuttingDown()) {
                    log.info("通过JVM关闭钩子触发优雅停机");

                    // 设置停机状态
                    RequestTrackingFilter.setShuttingDown(true);

                    // 等待请求完成
                    boolean completed = RequestTrackingFilter.waitForRequestsToComplete(10000);

                    if (completed) {
                        log.info("所有请求已完成");
                    } else {
                        log.warn("等待请求完成超时");
                    }
                }
            } catch (Exception e) {
                log.error("JVM关闭钩子执行异常", e);
            }

        }, "GracefulShutdownHook"));
    }
}
```

**为什么需要关闭钩子？**

1. **兜底机制**：当 Spring 容器关闭事件没有触发时的备用方案
2. **信号处理**：处理 SIGTERM、SIGINT 等系统信号
3. **异常情况**：处理应用异常退出的情况
4. **容器环境**：在 Docker/Kubernetes 环境中确保优雅停机

### 6. Kubernetes 集成原理

#### 6.1 Kubernetes Pod 生命周期

```yaml
# Pod的生命周期状态转换
Pending → Running → Terminating → Terminated
↓        ↓         ↓           ↓
创建中    运行中     停机中      已停止
```

**停机流程详解：**

```bash
# 1. kubectl delete pod 或者滚动更新
kubectl delete pod my-app-pod

# 2. Pod状态变为Terminating
# 3. 执行preStop钩子（如果配置了）
/bin/sh -c "sleep 5"

# 4. 发送SIGTERM信号给容器内的主进程
kill -TERM 1

# 5. 等待terminationGracePeriodSeconds时间
# 6. 如果进程还没退出，发送SIGKILL信号强制杀死
kill -KILL 1
```

#### 6.2 信号传递机制

```java
// 信号处理的内部机制
public class SignalHandling {

    static {
        // JVM会自动将SIGTERM信号转换为关闭钩子的执行
        // 这就是为什么我们的关闭钩子能够被触发的原因
    }

    // 当收到SIGTERM信号时的处理流程
    public void handleSIGTERM() {
        // 1. JVM接收到SIGTERM信号
        // 2. JVM开始关闭流程
        // 3. 执行所有注册的关闭钩子
        // 4. Spring容器的关闭钩子会触发ContextClosedEvent
        // 5. 我们的GracefulShutdownManager收到事件并开始优雅停机
    }
}
```

**时序配合：**

```
Kubernetes发送SIGTERM
    ↓ (立即)
JVM关闭钩子开始执行
    ↓ (几毫秒)
Spring ContextClosedEvent触发
    ↓ (立即)
GracefulShutdownManager开始执行
    ↓ (立即)
RequestTrackingFilter设置停机状态
    ↓ (0-30秒)
等待HTTP请求完成
    ↓ (0-10秒)
关闭Dubbo Consumer
    ↓ (几毫秒)
Spring容器完成关闭
    ↓ (几毫秒)
JVM进程退出
```

**配置要点：**

1. **terminationGracePeriodSeconds**：必须大于我们的总超时时间
2. **preStop 钩子**：给负载均衡器时间更新路由表
3. **就绪探针**：确保停机时能够及时从负载均衡中移除

这些底层原理的理解对于：

- **问题排查**：当优雅停机不工作时，知道从哪里开始调试
- **参数调优**：根据应用特点调整超时时间和线程池配置
- **监控告警**：知道监控哪些关键指标
- **扩展定制**：根据特殊需求进行框架扩展

都非常重要！

### 7. WAR 部署和类加载器原理

#### 7.1 Tomcat 类加载器层次结构

```
Bootstrap ClassLoader (JVM核心类)
    ↓
System ClassLoader (应用类路径)
    ↓
Common ClassLoader (Tomcat共享类)
    ↓
Catalina ClassLoader (Tomcat内部类)
    ↓
Shared ClassLoader (所有Web应用共享)
    ↓
WebApp ClassLoader (单个Web应用)
```

**关键理解点：**

1. **类路径隔离**：每个 WAR 应用都有独立的 WebApp ClassLoader
2. **父委托机制**：子类加载器优先委托父类加载器加载类
3. **可见性限制**：WebApp ClassLoader 无法访问 Catalina ClassLoader 中的类

#### 7.2 为什么不能使用 Tomcat 内部类

```java
// 这样的代码在WAR部署中会失败
import org.apache.catalina.valves.ValveBase;  // ClassNotFoundException!

public class MyValve extends ValveBase {
    // 编译时可能通过，但运行时会失败
    // 因为WebApp ClassLoader找不到ValveBase类
}
```

**失败原因分析：**

1. **编译时**：如果项目依赖中包含了 Tomcat 的 jar 包，编译可以通过
2. **运行时**：WAR 部署时，Tomcat 内部类在 Catalina ClassLoader 中，WebApp ClassLoader 无法访问
3. **异常表现**：`ClassNotFoundException` 或 `NoClassDefFoundError`

#### 7.3 正确的解决方案

```java
// 使用标准Servlet API，所有容器都支持
public class RequestTrackingFilter implements Filter {
    // Filter接口在javax.servlet包中，WebApp ClassLoader可以访问

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        // 使用标准API，确保兼容性
    }
}
```

**设计原则：**

1. **只使用标准 API**：javax.servlet、javax.servlet.http 等
2. **避免容器特定类**：不依赖 Tomcat、Jetty 等特定实现
3. **保持可移植性**：代码可以在任何 Servlet 容器中运行

#### 7.4 类加载器可见性测试

```java
// 用于调试类加载器问题的工具方法
public class ClassLoaderDebugger {

    public static void printClassLoaderHierarchy(Class<?> clazz) {
        ClassLoader cl = clazz.getClassLoader();
        System.out.println("类: " + clazz.getName());

        int level = 0;
        while (cl != null) {
            System.out.println("  ".repeat(level) + "ClassLoader: " + cl.getClass().getName());
            cl = cl.getParent();
            level++;
        }
        System.out.println("  ".repeat(level) + "Bootstrap ClassLoader");
    }

    public static boolean canLoadClass(String className) {
        try {
            Class.forName(className);
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    // 测试Tomcat内部类是否可访问
    public static void testTomcatClassAccess() {
        String[] tomcatClasses = {
            "org.apache.catalina.valves.ValveBase",
            "org.apache.catalina.connector.Request",
            "org.apache.catalina.connector.Response"
        };

        for (String className : tomcatClasses) {
            boolean accessible = canLoadClass(className);
            System.out.println(className + " 可访问: " + accessible);
        }
    }
}
```

**实际测试结果：**

```bash
# 在WAR部署的应用中运行
org.apache.catalina.valves.ValveBase 可访问: false
org.apache.catalina.connector.Request 可访问: false
org.apache.catalina.connector.Response 可访问: false

# 在Spring Boot嵌入式Tomcat中运行
org.apache.catalina.valves.ValveBase 可访问: true
org.apache.catalina.connector.Request 可访问: true
org.apache.catalina.connector.Response 可访问: true
```

这就是为什么我们的框架专门为传统 WAR 部署设计，使用纯 Servlet API 实现所有功能！

## WAR 包在 Kubernetes 中的完整停机流程

### 概述

当 WAR 包部署在 Kubernetes 中时，停机流程涉及多个层面的协调工作。让我们从 K8s 发出终止信号开始，详细分析每个步骤中各个组件的行为。

### 完整时序图

```
kubectl delete pod / 滚动更新触发
    ↓ (0ms)
┌─────────────────────────────────────────────────────────────┐
│ Kubernetes Master 层面                                      │
│ 1. Pod状态: Running → Terminating                           │
│ 2. 从Service Endpoints中移除Pod                             │
│ 3. 执行preStop钩子（如果配置了）                             │
└─────────────────────────────────────────────────────────────┘
    ↓ (preStop执行完成，通常5-10秒)
┌─────────────────────────────────────────────────────────────┐
│ Container Runtime 层面 (Docker/containerd)                  │
│ 4. 向容器内PID 1进程发送SIGTERM信号                          │
└─────────────────────────────────────────────────────────────┘
    ↓ (立即，几毫秒)
┌─────────────────────────────────────────────────────────────┐
│ JVM 层面                                                    │
│ 5. JVM接收SIGTERM信号                                       │
│ 6. 触发JVM关闭流程                                          │
│ 7. 执行所有注册的ShutdownHook（并行执行）                    │
└─────────────────────────────────────────────────────────────┘
    ↓ (几毫秒到几秒，取决于ShutdownHook数量)
┌─────────────────────────────────────────────────────────────┐
│ Spring 容器层面                                             │
│ 8. Spring的ShutdownHook被执行                               │
│ 9. 发布ContextClosedEvent事件                               │
│ 10. 我们的GracefulShutdownManager收到事件                   │
└─────────────────────────────────────────────────────────────┘
    ↓ (立即)
┌─────────────────────────────────────────────────────────────┐
│ 我们的优雅停机框架                                           │
│ 11. RequestTrackingFilter.setShuttingDown(true)            │
│ 12. 拒绝新的HTTP请求                                        │
│ 13. 等待现有HTTP请求完成                                     │
└─────────────────────────────────────────────────────────────┘
    ↓ (0-30秒，取决于配置和请求处理时间)
┌─────────────────────────────────────────────────────────────┐
│ Dubbo Consumer 关闭                                         │
│ 14. 等待正在进行的RPC调用完成                                │
│ 15. 关闭与Provider的连接                                    │
└─────────────────────────────────────────────────────────────┘
    ↓ (0-10秒，取决于配置)
┌─────────────────────────────────────────────────────────────┐
│ Spring 容器继续关闭                                         │
│ 16. 调用所有Bean的@PreDestroy方法                           │
│ 17. 销毁Bean实例                                            │
│ 18. 关闭ApplicationContext                                  │
└─────────────────────────────────────────────────────────────┘
    ↓ (几秒)
┌─────────────────────────────────────────────────────────────┐
│ Tomcat 关闭                                                 │
│ 19. 关闭所有Connector                                       │
│ 20. 停止所有Context                                         │
│ 21. 关闭线程池                                              │
│ 22. 清理资源                                                │
└─────────────────────────────────────────────────────────────┘
    ↓ (几秒)
┌─────────────────────────────────────────────────────────────┐
│ JVM 完全退出                                                │
│ 23. 所有ShutdownHook执行完成                                │
│ 24. JVM进程退出                                             │
└─────────────────────────────────────────────────────────────┘
    ↓ (立即)
┌─────────────────────────────────────────────────────────────┐
│ Kubernetes 清理                                             │
│ 25. 容器状态变为Terminated                                  │
│ 26. Pod被删除                                               │
└─────────────────────────────────────────────────────────────┘
```

### 各层面详细分析

#### 1. Kubernetes Master 层面 (0-10 秒)

```yaml
# 当执行以下命令时
kubectl delete pod my-app-pod
# 或者滚动更新时
kubectl rollout restart deployment my-app
```

**K8s 内部发生的事情：**

```go
// Kubernetes内部逻辑（简化版）
func (kl *Kubelet) killPod(pod *v1.Pod) {
    // 1. 更新Pod状态
    pod.Status.Phase = v1.PodTerminating

    // 2. 从Service Endpoints中移除
    kl.removeFromEndpoints(pod)

    // 3. 执行preStop钩子
    if pod.Spec.Containers[0].Lifecycle.PreStop != nil {
        kl.executePreStopHook(pod)
    }

    // 4. 发送SIGTERM信号
    kl.sendSignalToPod(pod, syscall.SIGTERM)

    // 5. 等待terminationGracePeriodSeconds
    time.Sleep(pod.Spec.TerminationGracePeriodSeconds)

    // 6. 如果还没退出，发送SIGKILL
    if kl.isPodRunning(pod) {
        kl.sendSignalToPod(pod, syscall.SIGKILL)
    }
}
```

**关键时间点：**

- **0ms**: Pod 状态变为 Terminating
- **0-100ms**: 从 Service Endpoints 移除（负载均衡器停止转发流量）
- **0-5000ms**: 执行 preStop 钩子（如果配置了）

#### 2. JVM 信号处理层面 (立即响应)

```java
// JVM内部信号处理机制
public class JVMSignalHandler {

    static {
        // JVM启动时注册信号处理器
        Signal.handle(new Signal("TERM"), new SignalHandler() {
            public void handle(Signal sig) {
                // 触发JVM关闭流程
                Runtime.getRuntime().exit(0);
            }
        });
    }

    // Runtime.exit()内部实现
    public void exit(int status) {
        // 1. 设置关闭状态
        shutdown = true;

        // 2. 执行所有ShutdownHook（并行）
        runShutdownHooks();

        // 3. 运行finalizer
        runFinalization();

        // 4. 退出JVM
        halt(status);
    }

    private void runShutdownHooks() {
        // 并行启动所有钩子
        for (Thread hook : shutdownHooks) {
            hook.start();
        }

        // 等待所有钩子完成
        for (Thread hook : shutdownHooks) {
            try {
                hook.join();
            } catch (InterruptedException e) {
                // 忽略中断
            }
        }
    }
}
```

#### 3. Spring 容器关闭层面

```java
// Spring的关闭钩子实现
public class SpringShutdownHook extends Thread {

    private final ConfigurableApplicationContext context;

    @Override
    public void run() {
        // 1. 发布容器关闭事件
        context.publishEvent(new ContextClosedEvent(context));

        // 2. 停止所有Lifecycle Bean
        getLifecycleProcessor().onClose();

        // 3. 销毁单例Bean
        destroyBeans();

        // 4. 关闭BeanFactory
        closeBeanFactory();
    }

    private void destroyBeans() {
        // 按依赖关系逆序销毁Bean
        String[] beanNames = getBeanNamesForType(Object.class);

        for (int i = beanNames.length - 1; i >= 0; i--) {
            String beanName = beanNames[i];
            Object bean = getBean(beanName);

            // 调用@PreDestroy方法
            invokePreDestroyMethods(bean);

            // 调用DisposableBean.destroy()
            if (bean instanceof DisposableBean) {
                ((DisposableBean) bean).destroy();
            }
        }
    }
}
```

#### 4. Tomcat 关闭层面

```java
// Tomcat的关闭流程
public class TomcatShutdown {

    public void shutdown() {
        // 1. 停止接收新连接
        for (Connector connector : connectors) {
            connector.pause();
            log.info("Connector {} 已暂停", connector.getPort());
        }

        // 2. 等待现有请求完成（如果配置了优雅停机）
        waitForRequestsToComplete();

        // 3. 停止所有Connector
        for (Connector connector : connectors) {
            connector.stop();
            log.info("Connector {} 已停止", connector.getPort());
        }

        // 4. 停止所有Context（Web应用）
        for (Context context : contexts) {
            context.stop();
            log.info("Context {} 已停止", context.getPath());
        }

        // 5. 关闭线程池
        executor.shutdown();
        try {
            if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
        }

        // 6. 清理资源
        cleanup();
    }
}
```

### 我们的框架介入时机

```java
// 我们的GracefulShutdownManager在Spring关闭事件中被调用
@Component
public class GracefulShutdownManager implements ApplicationListener<ContextClosedEvent> {

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        // 此时的调用栈：
        // JVM ShutdownHook Thread
        //   └── SpringShutdownHook.run()
        //       └── ApplicationContext.publishEvent(ContextClosedEvent)
        //           └── GracefulShutdownManager.onApplicationEvent() ← 我们在这里

        log.info(">>>>> 收到Spring容器关闭事件，开始执行优雅停机流程...");

        // 1. 设置停机状态（立即生效）
        RequestTrackingFilter.setShuttingDown(true);

        // 2. 等待HTTP请求完成
        boolean httpCompleted = RequestTrackingFilter.waitForRequestsToComplete(30000);

        // 3. Dubbo由自身的ShutdownHook处理关闭（并发执行）
        log.info(">>>>> Dubbo Consumer关闭由Dubbo自身的ShutdownHook处理");

        // 4. 方法返回，Spring继续其关闭流程
        log.info(">>>>> 优雅停机流程完成，返回给Spring容器");
    }
}
```

### 关键时间节点分析

#### 时间窗口计算

```bash
# 总的优雅停机时间 = preStop + 应用关闭时间
# 必须 < terminationGracePeriodSeconds

preStop钩子时间:                    5秒   (给负载均衡器更新时间)
HTTP请求完成等待:                  30秒   (http-request-timeout-ms)
Dubbo自动关闭:                     并发   (由Dubbo ShutdownHook处理)
Spring容器关闭:                     5秒   (Bean销毁、资源清理)
Tomcat关闭:                        5秒   (连接关闭、线程池关闭)
缓冲时间:                          5秒   (预留给其他操作)
─────────────────────────────────────────
总计:                             60秒

# 因此 terminationGracePeriodSeconds 应该设置为 >= 60秒
```

#### 实际日志输出示例

```bash
# === Kubernetes层面 ===
# kubectl delete pod my-app-xxx 执行后

# === preStop钩子执行 ===
2025-07-23 10:30:00.000 INFO [preStop] 执行preStop钩子: sleep 5
2025-07-23 10:30:05.000 INFO [preStop] preStop钩子执行完成

# === JVM接收SIGTERM信号 ===
2025-07-23 10:30:05.001 INFO [JVM] 接收到SIGTERM信号，开始关闭流程

# === Spring容器关闭开始 ===
2025-07-23 10:30:05.010 INFO [SpringShutdownHook] Spring容器开始关闭...
2025-07-23 10:30:05.011 INFO [ApplicationContext] 发布ContextClosedEvent事件

# === 我们的优雅停机框架开始工作 ===
2025-07-23 10:30:05.012 INFO [GracefulShutdownManager] >>>>> 收到Spring容器关闭事件，开始执行优雅停机流程...
2025-07-23 10:30:05.013 INFO [GracefulShutdownManager] >>>>> 步骤1: 设置停机状态，停止接收新的HTTP请求...
2025-07-23 10:30:05.014 INFO [RequestTrackingFilter] >>>>> 已设置停机状态，新请求将被拒绝
2025-07-23 10:30:05.015 INFO [GracefulShutdownManager] >>>>> 当前请求状态 - 活跃请求数: 3, 总处理请求数: 1247

# === 等待HTTP请求完成 ===
2025-07-23 10:30:05.016 INFO [GracefulShutdownManager] >>>>> 步骤2: 等待现有HTTP请求处理完成...
2025-07-23 10:30:06.234 DEBUG [RequestTrackingFilter] >>>>> 请求结束，剩余活跃请求数: 2
2025-07-23 10:30:07.891 DEBUG [RequestTrackingFilter] >>>>> 请求结束，剩余活跃请求数: 1
2025-07-23 10:30:09.156 DEBUG [RequestTrackingFilter] >>>>> 请求结束，剩余活跃请求数: 0
2025-07-23 10:30:09.157 INFO [RequestTrackingFilter] >>>>> 所有HTTP请求已处理完成，活跃请求数: 0
2025-07-23 10:30:09.158 INFO [GracefulShutdownManager] >>>>> HTTP请求处理完成

# === Dubbo 自动关闭（并发执行） ===
2025-07-23 10:30:09.159 INFO [GracefulShutdownManager] >>>>> Dubbo Consumer关闭由Dubbo自身的ShutdownHook处理
# 注意：Dubbo的ShutdownHook与Spring的ShutdownHook并发执行
# Dubbo会自动：停止接收新RPC请求 → 等待现有RPC调用完成 → 关闭连接

# === 优雅停机框架完成 ===
2025-07-23 10:30:11.237 INFO [GracefulShutdownManager] >>>>> 优雅停机流程完成，总耗时: 6225ms
2025-07-23 10:30:11.238 INFO [GracefulShutdownManager] >>>>> 优雅停机流程完成，返回给Spring容器

# === Spring容器继续关闭 ===
2025-07-23 10:30:11.240 INFO [ApplicationContext] 开始销毁Bean...
2025-07-23 10:30:11.245 INFO [Bean] 调用UserService的@PreDestroy方法
2025-07-23 10:30:11.250 INFO [Bean] 调用DataSource的destroy方法
2025-07-23 10:30:12.100 INFO [ApplicationContext] 所有Bean已销毁
2025-07-23 10:30:12.101 INFO [ApplicationContext] Spring容器关闭完成

# === Tomcat关闭 ===
2025-07-23 10:30:12.102 INFO [Tomcat] 开始关闭Tomcat...
2025-07-23 10:30:12.103 INFO [Connector] Connector http-nio-8080 已暂停
2025-07-23 10:30:12.104 INFO [Connector] Connector http-nio-8080 已停止
2025-07-23 10:30:12.200 INFO [Context] Context /my-app 已停止
2025-07-23 10:30:13.500 INFO [ThreadPool] 线程池已关闭
2025-07-23 10:30:13.501 INFO [Tomcat] Tomcat关闭完成

# === JVM退出 ===
2025-07-23 10:30:13.502 INFO [JVM] 所有ShutdownHook执行完成
2025-07-23 10:30:13.503 INFO [JVM] JVM进程退出

# === Kubernetes清理 ===
# Pod状态变为Terminated，容器被删除
```

### 并发执行分析

#### JVM ShutdownHook 并发执行

```java
// 多个ShutdownHook会并行执行
public class ShutdownHookAnalysis {

    public static void main(String[] args) {
        // Spring的关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("[SpringHook] 开始执行 - " + Thread.currentThread().getName());
            // Spring容器关闭逻辑
            System.out.println("[SpringHook] 执行完成");
        }, "SpringShutdownHook"));

        // 我们自定义的关闭钩子（如果有）
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("[CustomHook] 开始执行 - " + Thread.currentThread().getName());
            // 自定义清理逻辑
            System.out.println("[CustomHook] 执行完成");
        }, "CustomShutdownHook"));

        // 其他第三方库的关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("[ThirdPartyHook] 开始执行 - " + Thread.currentThread().getName());
            // 第三方库清理逻辑
            System.out.println("[ThirdPartyHook] 执行完成");
        }, "ThirdPartyShutdownHook"));
    }
}

// 输出示例（并行执行，顺序不确定）：
// [SpringHook] 开始执行 - SpringShutdownHook
// [CustomHook] 开始执行 - CustomShutdownHook
// [ThirdPartyHook] 开始执行 - ThirdPartyShutdownHook
// [CustomHook] 执行完成
// [ThirdPartyHook] 执行完成
// [SpringHook] 执行完成  ← Spring的钩子通常最慢，因为要处理整个容器
```

### 异常情况处理

#### 1. 超时强制终止

```bash
# 如果应用在terminationGracePeriodSeconds时间内没有退出
2025-07-23 10:31:05.000 WARN [Kubernetes] 应用未在60秒内退出，发送SIGKILL信号
2025-07-23 10:31:05.001 INFO [JVM] 接收到SIGKILL信号，立即终止
# 进程被强制杀死，没有任何清理机会
```

#### 2. 请求等待超时

```bash
2025-07-23 10:30:35.016 WARN [GracefulShutdownManager] >>>>> HTTP请求等待超时，仍有2个活跃请求
2025-07-23 10:30:35.017 INFO [GracefulShutdownManager] >>>>> 继续后续流程，强制关闭
```

#### 3. 应用关闭异常

```bash
2025-07-23 10:30:11.160 ERROR [ApplicationContext] >>>>> Bean销毁过程中发生异常
2025-07-23 10:30:11.161 WARN [GracefulShutdownManager] >>>>> 优雅停机过程中发生异常，继续关闭流程
```

### 监控和调试

#### 关键监控指标

```bash
# 1. 停机总时长
graceful_shutdown_duration_seconds{phase="total"} 6.225

# 2. HTTP请求等待时长
graceful_shutdown_duration_seconds{phase="http_requests"} 4.142

# 3. Spring容器关闭时长
graceful_shutdown_duration_seconds{phase="spring_context"} 2.074

# 4. 停机时的活跃请求数
graceful_shutdown_active_requests_at_start 3

# 5. 是否超时
graceful_shutdown_timeout{phase="http_requests"} 0
graceful_shutdown_timeout{phase="spring_context"} 0
```

#### 调试命令

```bash
# 1. 查看Pod停机过程
kubectl describe pod my-app-xxx

# 2. 查看应用日志
kubectl logs my-app-xxx --follow

# 3. 查看Pod事件
kubectl get events --field-selector involvedObject.name=my-app-xxx

# 4. 实时监控停机过程
kubectl get pod my-app-xxx -w
```

这个完整的停机流程分析帮助团队成员理解：

- **每个层面的职责**：K8s、JVM、Spring、Tomcat 各自负责什么
- **时间窗口规划**：如何合理配置各种超时时间
- **并发执行机制**：哪些操作是并行的，哪些是串行的
- **异常情况处理**：当某个环节失败时会发生什么
- **监控和调试**：如何观察和排查停机过程中的问题

## 完整停机流程总结

### 🎯 26 个关键步骤的时序分析

#### 🔄 层面划分

1. **Kubernetes Master 层面** (0-10 秒)
2. **Container Runtime 层面** (立即)
3. **JVM 层面** (几毫秒)
4. **Spring 容器层面** (立即响应)
5. **我们的优雅停机框架** (0-40 秒)
6. **Tomcat 关闭层面** (几秒)
7. **JVM 完全退出** (立即)
8. **Kubernetes 清理** (立即)

#### ⏱️ 关键时间节点

```bash
T+0ms:    kubectl delete pod 执行
T+0-100ms: 从Service Endpoints移除Pod
T+5000ms: preStop钩子执行完成
T+5001ms: SIGTERM信号发送给JVM
T+5010ms: Spring容器开始关闭
T+5012ms: 我们的优雅停机框架开始工作
T+9158ms: 所有HTTP请求处理完成
T+11236ms: Dubbo Consumer关闭完成
T+13503ms: JVM进程完全退出
```

### 🔍 深度技术解析

#### 1. 信号传递机制

- **SIGTERM → JVM 信号处理器 → Runtime.exit() → ShutdownHook 并行执行**
- **Spring 的 ShutdownHook → ContextClosedEvent → 我们的 GracefulShutdownManager**

#### 2. 并发执行特点

- **JVM 层面**：多个 ShutdownHook 并行执行，无法控制顺序
- **Spring 层面**：事件监听器按注册顺序串行执行
- **我们的框架**：在 Spring 事件中串行执行，确保顺序

#### 3. 时间窗口计算

```bash
preStop钩子:        5秒   (负载均衡器更新)
HTTP请求等待:      30秒   (业务请求完成)
Dubbo关闭:        10秒   (RPC连接关闭)
Spring+Tomcat:    10秒   (容器和服务器关闭)
缓冲时间:          5秒   (预留)
─────────────────────────
总计:             60秒   (terminationGracePeriodSeconds)
```

### 💡 关键理解点

#### 为什么这个顺序是正确的？

1. **preStop 先执行**：给负载均衡器时间更新路由表，避免新流量进入
2. **HTTP 请求优先处理**：用户体验最重要，确保 Web 请求不丢失
3. **Dubbo Consumer 后关闭**：RPC 调用通常有重试机制，容错性更好
4. **Spring 容器最后清理**：确保所有业务逻辑完成后再清理资源

#### 我们的框架在哪个环节介入？

```java
// 调用栈分析
SIGTERM信号
  └── JVM关闭流程
      └── SpringShutdownHook.run()
          └── ApplicationContext.publishEvent(ContextClosedEvent)
              └── GracefulShutdownManager.onApplicationEvent() ← 我们在这里介入
                  ├── RequestTrackingFilter.setShuttingDown(true)
                  ├── 等待HTTP请求完成
                  ├── 关闭Dubbo Consumer
                  └── 返回给Spring继续关闭流程
```

### 🚨 异常情况处理

#### 1. 超时保护机制

- **应用级超时**：我们的框架有 HTTP 和 Dubbo 的超时配置
- **K8s 级超时**：terminationGracePeriodSeconds 后强制 SIGKILL
- **多层保护**：确保应用不会无限期挂起

#### 2. 失败降级策略

- **HTTP 请求超时**：记录警告，继续后续流程
- **Dubbo 关闭失败**：记录错误，不阻塞 Spring 容器关闭
- **容错设计**：单个环节失败不影响整体停机流程

### 📊 监控和调试价值

#### 生产环境监控

- **停机时长监控**：确保在合理时间内完成
- **请求丢失监控**：验证优雅停机的有效性
- **异常情况告警**：及时发现配置或代码问题

#### 问题排查能力

- **详细的时间戳日志**：精确定位每个环节的耗时
- **分阶段监控指标**：识别性能瓶颈
- **K8s 命令工具**：观察 Pod 生命周期变化

### 🎯 关键特点

#### 对团队的价值：

1. **理解每个组件的职责边界**
2. **合理配置各种超时参数**
3. **快速定位停机过程中的问题**
4. **优化应用的关闭性能**
5. **确保在 K8s 环境中的稳定运行**

#### 技术架构优势：

- ✅ **专门适配**：针对传统 Spring MVC 架构设计，不依赖 Spring Boot
- ✅ **完整解决方案**：从 HTTP 请求到 Dubbo Consumer 的完整优雅停机
- ✅ **Kubernetes 就绪**：完美配合 K8s 的部署和停机机制
- ✅ **易于集成**：最小化配置，最大化兼容性
- ✅ **生产验证**：提供详细的监控、日志和故障排查指南

### 📊 与 Spring Boot 版本的对比

| 特性         | Spring Boot 版本            | 我们的解决方案   |
| ------------ | --------------------------- | ---------------- |
| 自动配置     | ✅ 内置支持                 | ✅ 自动配置类    |
| 请求跟踪     | ✅ 内置实现                 | ✅ 自实现 Filter |
| 优雅停机     | ✅ server.shutdown=graceful | ✅ 完整实现      |
| Dubbo 集成   | ⚠️ 需要额外配置             | ✅ 原生支持      |
| 传统架构支持 | ❌ 不适用                   | ✅ 专门设计      |

这套解决方案为传统 Spring MVC 应用提供了与 Spring Boot 同等级别的优雅停机能力，特别适合：

- ✅ 传统 Spring MVC 应用迁移到 Kubernetes
- ✅ 需要优雅停机但不想升级到 Spring Boot 的场景
- ✅ Dubbo Consumer 应用的优雅停机需求
- ✅ 对停机流程有精确控制需求的场景

现在团队成员对 WAR 包在 Kubernetes 中的优雅停机有了完整而深入的理解！

## 资源销毁时机深度解析

### 概述

在优雅停机过程中，除了 HTTP 请求和 RPC 调用，还有很多其他资源需要正确关闭，如数据库连接、Redis 连接、消息队列连接、线程池等。理解这些资源的销毁时机对于确保应用的完整优雅停机非常重要。

### 完整的资源销毁顺序

```bash
SIGTERM信号接收
    ↓
JVM关闭钩子并行执行
    ├── Spring容器关闭钩子
    ├── Tomcat关闭钩子
    └── 其他第三方库关闭钩子
    ↓
Spring容器关闭流程开始
    ↓
1. 发布ContextClosedEvent事件
    ├── 我们的GracefulShutdownManager执行 ← 这里
    │   ├── 停止接收新HTTP请求
    │   ├── 等待现有HTTP请求完成
    │   └── 关闭Dubbo Consumer
    └── 其他ApplicationListener执行
    ↓
2. 停止所有Lifecycle Bean
    ├── @PreDestroy方法执行
    ├── DisposableBean.destroy()执行
    └── 自定义销毁方法执行
    ↓
3. 销毁单例Bean（按依赖关系逆序）
    ├── 业务Service Bean销毁
    ├── 数据访问层Bean销毁
    ├── 连接池Bean销毁 ← 数据库连接在这里关闭
    └── 配置Bean销毁
    ↓
4. 关闭BeanFactory
    ↓
Tomcat关闭
    ↓
JVM进程退出
```

### 各种连接的具体销毁时机

#### 1. 数据库连接池（如 HikariCP、Druid）

```java
// HikariCP的销毁过程
@Component
public class DataSourceLifecycleAnalysis {

    @Autowired
    private DataSource dataSource;

    // 数据库连接池的销毁时机：
    // 1. Spring容器关闭时
    // 2. 在我们的GracefulShutdownManager执行完成之后
    // 3. 在Bean销毁阶段

    @PreDestroy
    public void onDestroy() {
        log.info(">>>>> DataSource相关Bean开始销毁");
        // 此时连接池还没有关闭，可以执行最后的数据库操作
    }
}

// HikariCP内部销毁逻辑（简化版）
public class HikariDataSource implements DataSource, Closeable {

    @PreDestroy  // 或者实现DisposableBean
    public void close() {
        log.info(">>>>> HikariCP连接池开始关闭");

        // 1. 停止接收新的连接请求
        shutdown = true;

        // 2. 等待活跃连接归还到池中
        while (activeConnections > 0 && !timeout) {
            Thread.sleep(100);
        }

        // 3. 关闭池中的所有连接
        for (Connection conn : connectionPool) {
            conn.close();
        }

        log.info(">>>>> HikariCP连接池关闭完成");
    }
}
```

**时机：** Spring Bean 销毁阶段，在我们的优雅停机完成**之后**

#### 2. Redis 连接（如 Jedis、Lettuce）

```java
// Redis连接的销毁过程
@Configuration
public class RedisConnectionLifecycle {

    // Jedis连接池的销毁
    @Bean(destroyMethod = "close")
    public JedisPool jedisPool() {
        return new JedisPool(config, host, port);
    }

    // Lettuce连接的销毁
    @Bean(destroyMethod = "shutdown")
    public LettuceConnectionFactory lettuceConnectionFactory() {
        return new LettuceConnectionFactory(config);
    }
}

// 实际的销毁逻辑
public class JedisPool implements Closeable {

    @Override
    public void close() {
        log.info(">>>>> Jedis连接池开始关闭");

        // 1. 停止创建新连接
        closed = true;

        // 2. 关闭池中的所有连接
        for (Jedis jedis : pool) {
            jedis.close();
        }

        log.info(">>>>> Jedis连接池关闭完成");
    }
}
```

**时机：** Spring Bean 销毁阶段，通过`destroyMethod`或`@PreDestroy`

#### 3. 消息队列连接（如 RabbitMQ、Kafka）

```java
// RabbitMQ连接的销毁
@Component
public class RabbitMQConnectionLifecycle {

    @Autowired
    private ConnectionFactory connectionFactory;

    @PreDestroy
    public void closeConnections() {
        log.info(">>>>> RabbitMQ连接开始关闭");

        if (connectionFactory instanceof CachingConnectionFactory) {
            ((CachingConnectionFactory) connectionFactory).destroy();
        }

        log.info(">>>>> RabbitMQ连接关闭完成");
    }
}

// Kafka Consumer的销毁
@Component
public class KafkaConsumerLifecycle {

    @KafkaListener(topics = "user-topic")
    public void handleMessage(String message) {
        // 消息处理逻辑
    }

    @PreDestroy
    public void closeConsumer() {
        log.info(">>>>> Kafka Consumer开始关闭");
        // Spring Kafka会自动处理Consumer的关闭
        log.info(">>>>> Kafka Consumer关闭完成");
    }
}
```

**时机：** Spring Bean 销毁阶段，在消息处理完成后

#### 4. 线程池和定时任务

```java
// 线程池的销毁
@Configuration
public class ThreadPoolLifecycle {

    @Bean(destroyMethod = "shutdown")
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        return executor;
    }

    @PreDestroy
    public void shutdownExecutor() {
        log.info(">>>>> 线程池开始关闭");

        // 1. 停止接收新任务
        taskExecutor.shutdown();

        // 2. 等待现有任务完成
        try {
            if (!taskExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                // 3. 强制关闭
                taskExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            taskExecutor.shutdownNow();
        }

        log.info(">>>>> 线程池关闭完成");
    }
}

// 定时任务的销毁
@Component
public class ScheduledTaskLifecycle {

    @Scheduled(fixedRate = 60000)
    public void scheduledTask() {
        // 定时任务逻辑
    }

    @PreDestroy
    public void stopScheduledTasks() {
        log.info(">>>>> 定时任务开始停止");
        // Spring会自动停止@Scheduled任务
        log.info(">>>>> 定时任务停止完成");
    }
}
```

**时机：** Spring Bean 销毁阶段，会等待正在执行的任务完成

### 详细的时序图

```bash
T+0ms:     SIGTERM信号接收
T+5ms:     JVM关闭钩子开始执行
T+10ms:    Spring容器关闭开始
T+15ms:    ContextClosedEvent发布
T+16ms:    ├── GracefulShutdownManager开始执行
T+17ms:    │   ├── 停止接收新HTTP请求
T+4000ms:  │   ├── 等待HTTP请求完成
T+4001ms:  │   └── 关闭Dubbo Consumer
T+4002ms:  └── GracefulShutdownManager执行完成
T+4010ms:  开始Bean销毁阶段
T+4015ms:  ├── 业务Service的@PreDestroy方法执行
T+4020ms:  ├── Repository的@PreDestroy方法执行
T+4025ms:  ├── 数据库连接池开始关闭 ← 数据库连接在这里
T+4100ms:  ├── 数据库连接池关闭完成
T+4105ms:  ├── Redis连接池开始关闭 ← Redis连接在这里
T+4150ms:  ├── Redis连接池关闭完成
T+4155ms:  ├── 消息队列连接开始关闭 ← MQ连接在这里
T+4200ms:  ├── 消息队列连接关闭完成
T+4205ms:  ├── 线程池开始关闭 ← 线程池在这里
T+4300ms:  ├── 线程池关闭完成
T+4305ms:  └── 所有Bean销毁完成
T+4310ms:  Spring容器关闭完成
T+4400ms:  Tomcat关闭完成
T+4500ms:  JVM进程退出
```

### 关键理解点

#### 1. 为什么这个顺序是合理的？

1. **业务逻辑先完成**：HTTP 请求和 RPC 调用先完成，确保不丢失用户请求
2. **连接后关闭**：业务逻辑完成后再关闭数据库、Redis 等连接
3. **基础设施最后**：线程池、连接池等基础设施最后关闭

#### 2. 如果需要在连接关闭前做最后的操作？

```java
@Component
public class FinalDataPersistence {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // 在GracefulShutdownManager之后，但在连接池关闭之前执行
    @EventListener
    @Order(Ordered.LOWEST_PRECEDENCE) // 最低优先级，最后执行
    public void onContextClosed(ContextClosedEvent event) {
        log.info(">>>>> 执行最后的数据持久化操作");

        // 此时HTTP请求已完成，但数据库连接还可用
        // 可以执行最后的数据保存、缓存清理等操作

        try (Connection conn = dataSource.getConnection()) {
            // 最后的数据库操作
            saveShutdownLog(conn);
        }

        // 最后的Redis操作
        redisTemplate.opsForValue().set("last-shutdown", System.currentTimeMillis());

        log.info(">>>>> 最后的数据持久化操作完成");
    }
}
```

#### 3. 监控各种连接的关闭

```java
@Component
public class ConnectionMonitor {

    @EventListener
    public void onContextClosed(ContextClosedEvent event) {
        log.info(">>>>> 开始监控连接关闭过程");

        // 可以在这里记录各种连接的状态
        monitorDatabaseConnections();
        monitorRedisConnections();
        monitorMessageQueueConnections();
    }

    private void monitorDatabaseConnections() {
        // 监控数据库连接池状态
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikari = (HikariDataSource) dataSource;
            log.info(">>>>> 数据库连接池状态 - 活跃连接: {}, 空闲连接: {}",
                    hikari.getHikariPoolMXBean().getActiveConnections(),
                    hikari.getHikariPoolMXBean().getIdleConnections());
        }
    }
}
```

### 总结

**资源销毁的时机顺序：**

1. **T+16ms**: 我们的优雅停机开始（HTTP 请求处理、RPC 调用关闭）
2. **T+4002ms**: 我们的优雅停机完成
3. **T+4025ms**: 数据库连接池开始关闭
4. **T+4105ms**: Redis 连接池开始关闭
5. **T+4155ms**: 消息队列连接开始关闭
6. **T+4205ms**: 线程池开始关闭

**关键点：**

- ✅ **时序正确**：业务逻辑先完成，基础设施后关闭
- ✅ **连接可用**：在我们的优雅停机过程中，所有连接都还可用
- ✅ **自动管理**：Spring 会自动处理这些连接的关闭，无需手动干预
- ✅ **可扩展**：可以通过`@PreDestroy`或`ContextClosedEvent`监听器添加自定义清理逻辑

这样的设计确保了优雅停机过程中不会因为连接过早关闭而导致业务逻辑执行失败！
