package im.cu.api.shutdown.grace;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * HTTP请求跟踪过滤器
 * <p>
 * 职责：
 * - 跟踪活跃的HTTP请求数量
 * - 在优雅停机时拒绝新请求
 * - 提供请求状态监控接口
 * <p>
 * 适用场景：
 * - Tomcat 9 + Spring MVC 4.2
 * - 需要手动实现请求跟踪的场景
 * - 与GracefulShutdownManager配合使用
 * <p>
 *
 * <AUTHOR>
 * @date 2025/07/22
 */
@Slf4j
public class RequestTrackingFilter implements Filter {

    /**
     * 活跃请求计数器
     */
    private static final AtomicInteger activeRequests = new AtomicInteger(0);

    /**
     * 总请求计数器
     */
    private static final AtomicInteger totalRequests = new AtomicInteger(0);

    /**
     * 是否正在停机
     */
    private static final AtomicBoolean shuttingDown = new AtomicBoolean(false);

    /**
     * 用于等待请求完成的锁对象
     * 使用专门的锁对象而不是类锁，提高性能
     */
    private static final Object waitLock = new Object();

    /**
     * 是否启用详细日志
     */
    private boolean verboseLogging = false;

    /**
     * 优雅停机配置
     */
    private GracefulShutdownConfiguration config;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 从初始化参数中读取配置
        String verboseParam = filterConfig.getInitParameter("verboseLogging");
        if ("true".equalsIgnoreCase(verboseParam)) {
            verboseLogging = true;
        }

        log.info(">>>>> RequestTrackingFilter 已初始化，详细日志: {}", verboseLogging);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (!(request instanceof HttpServletRequest)) {
            chain.doFilter(request, response);
            return;
        }

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        // 检查是否正在停机
        if (shuttingDown.get()) {
            // 停机中，拒绝新请求
            httpResponse.setStatus(HttpServletResponse.SC_SERVICE_UNAVAILABLE);
            httpResponse.setHeader("Connection", "close");
            httpResponse.setContentType("text/plain; charset=UTF-8");
            // 使用配置的重试时间，如果没有配置则使用默认值30秒
            int retryAfter = (config != null) ? config.getRetryAfterSeconds() : 30;
            httpResponse.setHeader("Retry-After", String.valueOf(retryAfter));

            try {
                httpResponse.getWriter().write("Server is shutting down gracefully. Please retry later.");
            } catch (IOException e) {
                log.warn(">>>>> 写入停机响应时发生异常", e);
            }

            if (verboseLogging) {
                log.debug(">>>>> 拒绝新请求: {} {}, 原因: 服务器正在停机",
                        httpRequest.getMethod(), httpRequest.getRequestURI());
            }
            return;
        }

        // 请求开始
        int currentActive = activeRequests.incrementAndGet();
        int requestId = totalRequests.incrementAndGet();
        long startTime = System.currentTimeMillis();

        String requestInfo = String.format("%s %s", httpRequest.getMethod(), httpRequest.getRequestURI());

        if (verboseLogging) {
            log.debug(">>>>> 请求开始 [{}] {}, 当前活跃请求数: {}", requestId, requestInfo, currentActive);
        }

        try {
            // 继续处理请求
            chain.doFilter(request, response);

        } finally {
            // 请求结束
            int remainingActive = activeRequests.decrementAndGet();
            long duration = System.currentTimeMillis() - startTime;

            if (verboseLogging) {
                log.debug(">>>>> 请求结束 [{}] {}, 耗时: {}ms, 状态码: {}, 剩余活跃请求数: {}",
                        requestId, requestInfo, duration, httpResponse.getStatus(), remainingActive);
            }

            // 如果是最后一个请求且正在停机，记录特殊日志
            if (remainingActive == 0 && shuttingDown.get()) {
                log.info(">>>>> 所有HTTP请求已处理完成，活跃请求数: 0");
                // 通知等待的线程
                synchronized (waitLock) {
                    waitLock.notifyAll();
                }
            }
        }
    }

    @Override
    public void destroy() {
        log.info(">>>>> RequestTrackingFilter 已销毁，最终统计 - 总请求数: {}, 活跃请求数: {}",
                totalRequests.get(), activeRequests.get());
    }

    /**
     * 获取当前活跃请求数
     */
    public static int getActiveRequestCount() {
        return activeRequests.get();
    }

    /**
     * 获取总请求数
     */
    public static int getTotalRequestCount() {
        return totalRequests.get();
    }

    /**
     * 设置停机状态
     * 设置后，新的请求将被拒绝
     */
    public static void setShuttingDown(boolean shutDown) {
        boolean oldValue = shuttingDown.getAndSet(shutDown);
        if (!oldValue && shutDown) {
            log.info(">>>>> 已设置停机状态，新请求将被拒绝");
        }
    }

    /**
     * 是否正在停机
     */
    public static boolean isShuttingDown() {
        return shuttingDown.get();
    }

    /**
     * 等待所有活跃请求完成
     * 
     * @param timeoutMs 超时时间（毫秒）
     * @return true如果所有请求都完成了，false如果超时
     */
    public static boolean waitForRequestsToComplete(long timeoutMs) {
        long deadline = System.currentTimeMillis() + timeoutMs;

        while (activeRequests.get() > 0 && System.currentTimeMillis() < deadline) {
            try {
                synchronized (waitLock) {
                    long waitTime = Math.min(1000, deadline - System.currentTimeMillis());
                    if (waitTime > 0) {
                        waitLock.wait(waitTime);
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn(">>>>> 等待请求完成时被中断");
                return false;
            }
        }

        int remaining = activeRequests.get();
        if (remaining > 0) {
            log.warn(">>>>> 等待请求完成超时，仍有 {} 个活跃请求", remaining);
            return false;
        } else {
            log.info(">>>>> 所有HTTP请求已完成");
            return true;
        }
    }

    /**
     * 重置计数器（主要用于测试）
     */
    public static void reset() {
        activeRequests.set(0);
        totalRequests.set(0);
        shuttingDown.set(false);
    }

    /**
     * 获取请求统计信息
     */
    public static String getStatistics() {
        return String.format("活跃请求: %d, 总请求数: %d, 停机状态: %s",
                activeRequests.get(), totalRequests.get(), shuttingDown.get());
    }

    /**
     * 设置详细日志
     */
    public void setVerboseLogging(boolean verboseLogging) {
        this.verboseLogging = verboseLogging;
    }

    /**
     * 设置优雅停机配置
     * 通常由Spring容器或配置类调用
     */
    public void setConfig(GracefulShutdownConfiguration config) {
        this.config = config;
        if (config != null) {
            this.verboseLogging = config.isRequestTrackingVerbose();
        }
    }
}
